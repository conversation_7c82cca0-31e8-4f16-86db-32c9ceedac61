# 悬浮飞行器使用示例

## 快速开始

### 1. 启动服务器并加载插件
确保你的Bukkit/Spigot/Paper服务器已经加载了PatheticExample插件。

### 2. 基本使用流程

#### 步骤1：设置起点
```
/pathetic pos1
```
在你想要开始寻路的位置执行此命令。

#### 步骤2：设置终点
```
/pathetic pos2
```
在你想要到达的目标位置执行此命令。

#### 步骤3：启动悬浮飞行器寻路
```
/pathetic hover blocks
```
这将使用悬浮飞行器算法计算路径，并用淡蓝色玻璃方块显示路径。

或者使用实体移动演示：
```
/pathetic hover entity
```
这将创建一个末影珍珠实体，带着你沿着计算出的路径移动。

### 3. 对比测试

你可以对比普通寻路和悬浮飞行器寻路的差异：

#### 普通寻路
```
/pathetic start blocks   # 黄色玻璃显示
/pathetic start entity   # 钻石实体移动
```

#### 悬浮飞行器寻路
```
/pathetic hover blocks   # 淡蓝色玻璃显示
/pathetic hover entity   # 末影珍珠实体移动
```

## 测试场景建议

### 场景1：平坦地形
在平坦的地面上测试，观察悬浮飞行器如何保持在2格高度。

### 场景2：山地地形
在有高度变化的山地测试，观察：
- 悬浮飞行器如何处理高度差小于3格的地形
- 当高度差大于等于3格时，路径如何绕行

### 场景3：复杂建筑
在有建筑物的区域测试，观察：
- 悬浮飞行器如何在建筑物之间寻找路径
- 如何避开过低的通道

### 场景4：峡谷地形
在峡谷或深坑附近测试，观察悬浮飞行器的安全性。

## 预期行为

### 悬浮飞行器特点
1. **高度偏好**：优先选择在地面上方2格的路径
2. **高度限制**：只能在1-3格高度范围内移动
3. **地形适应**：无法通过高度差≥3格的地形
4. **安全性**：确保上方有足够空间通行

### 路径差异
- **普通寻路**：会贴着地面行走，可以爬楼梯、跳跃
- **悬浮飞行器**：保持悬浮状态，更平滑的路径，但受高度差限制

## 性能提示

1. **大距离寻路**：悬浮飞行器寻路可能比普通寻路稍慢，因为需要额外的高度检查
2. **复杂地形**：在地形变化频繁的区域，计算时间可能会增加
3. **内存使用**：高级过滤器会使用更多内存来存储相邻位置信息

## 故障排除

### 问题：找不到路径
**可能原因**：
- 起点或终点的高度差太大（≥3格）
- 目标区域没有合适的悬浮空间
- 路径被高大的障碍物阻挡

**解决方案**：
- 调整起点或终点位置
- 确保目标区域上方有足够空间
- 使用普通寻路作为对比

### 问题：路径不理想
**可能原因**：
- 地形复杂导致绕路
- 高度限制导致无法选择最短路径

**解决方案**：
- 这是正常行为，悬浮飞行器优先安全性
- 可以尝试调整起点或终点的高度

### 问题：性能问题
**解决方案**：
- 减少寻路距离
- 在配置中调整最大迭代次数
- 考虑使用基本过滤器而不是高级过滤器

## 自定义配置

如果你想修改悬浮飞行器的行为，可以在代码中调整参数：

```java
// 在PatheticCommand.java中找到这一行：
List.of(new AdvancedHoverCraftFilter())

// 替换为自定义配置：
List.of(new AdvancedHoverCraftFilter(
    3,  // 偏好3格高（而不是2格）
    2,  // 最大高度差2格（而不是3格）
    2,  // 最小悬浮高度2格
    4   // 最大悬浮高度4格
))
```

## 扩展想法

1. **添加燃料系统**：根据高度变化计算能量消耗
2. **天气影响**：在恶劣天气中增加路径成本
3. **载重限制**：根据载重调整最大悬浮高度
4. **多层路径**：支持在不同高度层之间切换
