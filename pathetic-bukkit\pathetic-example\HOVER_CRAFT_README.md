# 悬浮飞行器寻路系统

基于PatheticAPI实现的低空悬浮飞行器寻路功能。

## 功能特点

### 1. 悬浮高度控制
- 可以在方块正上方1、2、3格的高度运动
- 更偏好保持在2格高度（最优路径）
- 自动避免过低或过高的路径

### 2. 地形适应性
- 如果相邻位置的底部方块高度差大于等于3格，无法到达该位置
- 智能检测地面高度变化
- 确保有足够的上方空间用于飞行器通行

### 3. 两种过滤器实现

#### 基本悬浮飞行器过滤器 (HoverCraftFilter)
- 基础的高度检查和可通行性验证
- 简单的移动成本计算
- 适用于大多数场景

#### 高级悬浮飞行器过滤器 (AdvancedHoverCraftFilter)
- 包含基本过滤器的所有功能
- 检查8个相邻方向的高度差限制
- 更精确的移动成本计算
- 支持自定义参数配置

## 使用方法

### 1. 设置起点和终点
```
/pathetic pos1  # 设置起点为当前位置
/pathetic pos2  # 设置终点为当前位置
```

### 2. 启动悬浮飞行器寻路
```
/pathetic hover blocks   # 使用方块显示路径（蓝色玻璃）
/pathetic hover entity   # 使用实体移动演示（末影珍珠）
```

### 3. 对比普通寻路
```
/pathetic start blocks   # 普通寻路（黄色玻璃）
/pathetic start entity   # 普通寻路实体移动（钻石）
```

## 技术实现

### 核心算法
1. **地面检测**: 向下搜索最多15格，寻找固体方块作为地面参考
2. **高度验证**: 确保悬浮高度在1-3格范围内
3. **相邻检查**: 验证8个方向的地面高度差不超过限制
4. **成本计算**: 根据与偏好高度的差异计算路径成本

### 过滤器参数
- `preferredHeight`: 偏好的悬浮高度（默认2格）
- `maxHeightDifference`: 最大允许的高度差（默认3格）
- `minHoverHeight`: 最小悬浮高度（默认1格）
- `maxHoverHeight`: 最大悬浮高度（默认3格）

### 自定义配置示例
```java
// 创建自定义配置的悬浮飞行器过滤器
AdvancedHoverCraftFilter customFilter = new AdvancedHoverCraftFilter(
    3,  // 偏好3格高
    2,  // 最大高度差2格
    2,  // 最小悬浮高度2格
    4   // 最大悬浮高度4格
);
```

## 视觉效果

### 方块显示模式
- **普通寻路**: 黄色染色玻璃
- **悬浮飞行器**: 淡蓝色染色玻璃

### 实体移动模式
- **普通寻路**: 钻石物品展示实体
- **悬浮飞行器**: 末影珍珠物品展示实体（更大尺寸，更快移动）

## 应用场景

1. **无人机路径规划**: 模拟无人机在复杂地形中的飞行路径
2. **飞行载具导航**: 为飞行载具提供安全的低空飞行路线
3. **建筑规划**: 在建筑项目中规划空中通道
4. **游戏机制**: 为游戏中的飞行单位提供智能寻路

## 性能考虑

- 高级过滤器由于需要检查相邻位置，计算成本较高
- 建议在大规模寻路时使用基本过滤器
- 可以通过调整搜索深度来平衡性能和准确性

## 扩展可能

1. **动态高度调整**: 根据地形自动调整悬浮高度
2. **风力影响**: 考虑环境因素对飞行路径的影响
3. **能耗优化**: 根据高度变化计算能量消耗
4. **多层路径**: 支持多个高度层的路径规划
