package de.metaphoriker.pathetic.example.filter;

import de.metaphoriker.pathetic.api.pathing.filter.PathFilter;
import de.metaphoriker.pathetic.api.pathing.filter.PathValidationContext;
import de.metaphoriker.pathetic.api.provider.NavigationPointProvider;
import de.metaphoriker.pathetic.api.wrapper.PathPosition;
import de.metaphoriker.pathetic.bukkit.provider.BukkitNavigationPoint;

/**
 * 高级低空悬浮飞行器过滤器
 * 
 * 特点：
 * 1. 可以在方块正上方1、2、3格的高度运动，但更偏好保持在2格高
 * 2. 检查相邻位置之间的高度差，如果大于等于3格则无法到达
 * 3. 考虑移动方向和邻近节点的高度差限制
 */
public class AdvancedHoverCraftFilter implements PathFilter {

  private final int preferredHeight; // 偏好的悬浮高度
  private final int maxHeightDifference; // 最大高度差
  private final int minHoverHeight; // 最小悬浮高度
  private final int maxHoverHeight; // 最大悬浮高度

  /**
   * 构造函数
   * 
   * @param preferredHeight 偏好的悬浮高度（默认2格）
   * @param maxHeightDifference 最大允许的高度差（默认3格）
   * @param minHoverHeight 最小悬浮高度（默认1格）
   * @param maxHoverHeight 最大悬浮高度（默认3格）
   */
  public AdvancedHoverCraftFilter(int preferredHeight, int maxHeightDifference, int minHoverHeight, int maxHoverHeight) {
    this.preferredHeight = preferredHeight;
    this.maxHeightDifference = maxHeightDifference;
    this.minHoverHeight = minHoverHeight;
    this.maxHoverHeight = maxHoverHeight;
  }

  /**
   * 使用默认参数的构造函数
   */
  public AdvancedHoverCraftFilter() {
    this(2, 3, 1, 3); // 偏好2格高，最大高度差3格，悬浮范围1-3格
  }

  @Override
  public boolean filter(PathValidationContext context) {
    PathPosition position = context.getPosition();
    NavigationPointProvider provider = context.getNavigationPointProvider();

    // 基本可通行性检查
    if (!isBasicallyTraversable(position, provider)) {
      return false;
    }

    // 找到当前位置下方的地面
    PathPosition groundPosition = findGroundBelow(position, provider);
    if (groundPosition == null) {
      return false; // 找不到地面，不允许通过
    }

    // 计算当前位置相对于地面的高度
    int currentHeight = position.getFlooredY() - groundPosition.getFlooredY();
    
    // 检查高度是否在允许范围内
    if (currentHeight < minHoverHeight || currentHeight > maxHoverHeight) {
      return false;
    }

    // 检查相邻位置的高度差限制
    if (!checkAdjacentHeightDifferences(position, provider)) {
      return false;
    }

    return true;
  }

  /**
   * 检查基本的可通行性
   */
  private boolean isBasicallyTraversable(PathPosition position, NavigationPointProvider provider) {
    // 检查当前位置是否可通行
    BukkitNavigationPoint currentPoint = (BukkitNavigationPoint) provider.getNavigationPoint(position);
    if (!currentPoint.isTraversable()) {
      return false;
    }

    // 检查上方空间是否足够（飞行器需要额外的空间）
    PathPosition above = position.add(0, 1, 0);
    BukkitNavigationPoint abovePoint = (BukkitNavigationPoint) provider.getNavigationPoint(above);
    if (!abovePoint.isTraversable()) {
      return false;
    }

    return true;
  }

  /**
   * 检查相邻位置的高度差是否符合要求
   */
  private boolean checkAdjacentHeightDifferences(PathPosition position, NavigationPointProvider provider) {
    PathPosition currentGround = findGroundBelow(position, provider);
    if (currentGround == null) {
      return false;
    }

    // 检查8个相邻方向的高度差
    int[][] directions = {
        {-1, 0}, {1, 0}, {0, -1}, {0, 1}, // 四个基本方向
        {-1, -1}, {-1, 1}, {1, -1}, {1, 1} // 四个对角方向
    };

    for (int[] dir : directions) {
      PathPosition adjacentPos = position.add(dir[0], 0, dir[1]);
      PathPosition adjacentGround = findGroundBelow(adjacentPos, provider);
      
      if (adjacentGround != null) {
        int heightDiff = Math.abs(adjacentGround.getFlooredY() - currentGround.getFlooredY());
        
        // 如果相邻位置的地面高度差大于等于最大允许高度差，则当前位置不可用
        if (heightDiff >= maxHeightDifference) {
          return false;
        }
      }
    }

    return true;
  }

  /**
   * 寻找指定位置下方的地面（固体方块）
   */
  private PathPosition findGroundBelow(PathPosition position, NavigationPointProvider provider) {
    // 向下搜索最多15格，寻找固体方块
    for (int i = 0; i <= 15; i++) {
      PathPosition checkPos = position.subtract(0, i, 0);
      BukkitNavigationPoint point = (BukkitNavigationPoint) provider.getNavigationPoint(checkPos);
      
      if (point.getMaterial().isSolid()) {
        return checkPos;
      }
    }
    return null; // 找不到地面
  }

  /**
   * 计算位置的移动成本，偏好保持在指定高度
   */
  public double getMovementCost(PathPosition position, NavigationPointProvider provider) {
    PathPosition groundPosition = findGroundBelow(position, provider);
    if (groundPosition == null) {
      return Double.MAX_VALUE; // 找不到地面，成本最高
    }

    int currentHeight = position.getFlooredY() - groundPosition.getFlooredY();
    
    // 基础成本
    double cost = 1.0;
    
    // 根据与偏好高度的差异增加成本
    int heightDifference = Math.abs(currentHeight - preferredHeight);
    cost += heightDifference * 0.3; // 每偏离1格增加0.3成本
    
    // 如果高度过低或过高，增加额外成本
    if (currentHeight == minHoverHeight || currentHeight == maxHoverHeight) {
      cost += 0.5; // 边界高度增加额外成本
    }
    
    return cost;
  }

  /**
   * 检查从一个位置到另一个位置是否可以移动
   */
  public boolean canMoveBetween(PathPosition from, PathPosition to, NavigationPointProvider provider) {
    PathPosition fromGround = findGroundBelow(from, provider);
    PathPosition toGround = findGroundBelow(to, provider);
    
    if (fromGround == null || toGround == null) {
      return false;
    }

    // 计算地面高度差
    int heightDiff = Math.abs(toGround.getFlooredY() - fromGround.getFlooredY());
    
    // 如果高度差大于等于最大允许高度差，则不能移动
    return heightDiff < maxHeightDifference;
  }

  // Getter方法
  public int getPreferredHeight() {
    return preferredHeight;
  }

  public int getMaxHeightDifference() {
    return maxHeightDifference;
  }

  public int getMinHoverHeight() {
    return minHoverHeight;
  }

  public int getMaxHoverHeight() {
    return maxHoverHeight;
  }
}
