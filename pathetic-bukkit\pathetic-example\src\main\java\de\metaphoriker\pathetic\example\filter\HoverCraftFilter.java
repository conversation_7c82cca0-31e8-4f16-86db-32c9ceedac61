package de.metaphoriker.pathetic.example.filter;

import de.metaphoriker.pathetic.api.pathing.filter.PathFilter;
import de.metaphoriker.pathetic.api.pathing.filter.PathValidationContext;
import de.metaphoriker.pathetic.api.provider.NavigationPointProvider;
import de.metaphoriker.pathetic.api.wrapper.PathPosition;
import de.metaphoriker.pathetic.bukkit.provider.BukkitNavigationPoint;

/**
 * 低空悬浮飞行器过滤器
 * 
 * 特点：
 * 1. 可以在方块正上方1、2、3格的高度运动，但更偏好保持在2格高
 * 2. 如果下一个位置的底部方块相比当前底部方块的高度差大于等于3格，无法到达该位置
 */
public class HoverCraftFilter implements PathFilter {

  private final int preferredHeight; // 偏好的悬浮高度
  private final int maxHeightDifference; // 最大高度差

  /**
   * 构造函数
   * 
   * @param preferredHeight 偏好的悬浮高度（默认2格）
   * @param maxHeightDifference 最大允许的高度差（默认3格）
   */
  public HoverCraftFilter(int preferredHeight, int maxHeightDifference) {
    this.preferredHeight = preferredHeight;
    this.maxHeightDifference = maxHeightDifference;
  }

  /**
   * 使用默认参数的构造函数
   */
  public HoverCraftFilter() {
    this(2, 3); // 偏好2格高，最大高度差3格
  }

  @Override
  public boolean filter(PathValidationContext context) {
    PathPosition position = context.getPosition();
    NavigationPointProvider provider = context.getNavigationPointProvider();

    // 获取当前位置的导航点
    BukkitNavigationPoint currentPoint = (BukkitNavigationPoint) provider.getNavigationPoint(position);
    
    // 检查当前位置是否可通行
    if (!currentPoint.isTraversable()) {
      return false;
    }

    // 找到当前位置下方的固体方块（地面）
    PathPosition groundPosition = findGroundBelow(position, provider);
    if (groundPosition == null) {
      return false; // 找不到地面，不允许通过
    }

    // 计算当前位置相对于地面的高度
    int currentHeight = position.getFlooredY() - groundPosition.getFlooredY();
    
    // 检查高度是否在允许范围内（1-3格）
    if (currentHeight < 1 || currentHeight > 3) {
      return false;
    }

    // 检查上方空间是否足够（至少需要1格空间用于飞行器本体）
    PathPosition above = position.add(0, 1, 0);
    BukkitNavigationPoint abovePoint = (BukkitNavigationPoint) provider.getNavigationPoint(above);
    if (!abovePoint.isTraversable()) {
      return false;
    }

    return true;
  }

  /**
   * 检查从当前位置到目标位置的高度差是否允许
   * 
   * @param currentPos 当前位置
   * @param targetPos 目标位置
   * @param provider 导航点提供者
   * @return 是否允许移动
   */
  public boolean canMoveTo(PathPosition currentPos, PathPosition targetPos, NavigationPointProvider provider) {
    // 找到两个位置下方的地面
    PathPosition currentGround = findGroundBelow(currentPos, provider);
    PathPosition targetGround = findGroundBelow(targetPos, provider);
    
    if (currentGround == null || targetGround == null) {
      return false;
    }

    // 计算地面高度差
    int heightDifference = Math.abs(targetGround.getFlooredY() - currentGround.getFlooredY());
    
    // 如果高度差大于等于最大允许高度差，则不能移动
    return heightDifference < maxHeightDifference;
  }

  /**
   * 计算位置的移动成本，偏好保持在指定高度
   * 
   * @param position 位置
   * @param provider 导航点提供者
   * @return 移动成本（越低越好）
   */
  public double getMovementCost(PathPosition position, NavigationPointProvider provider) {
    PathPosition groundPosition = findGroundBelow(position, provider);
    if (groundPosition == null) {
      return Double.MAX_VALUE; // 找不到地面，成本最高
    }

    int currentHeight = position.getFlooredY() - groundPosition.getFlooredY();
    
    // 基础成本
    double cost = 1.0;
    
    // 根据与偏好高度的差异增加成本
    int heightDifference = Math.abs(currentHeight - preferredHeight);
    cost += heightDifference * 0.5; // 每偏离1格增加0.5成本
    
    return cost;
  }

  /**
   * 寻找指定位置下方的地面（固体方块）
   * 
   * @param position 起始位置
   * @param provider 导航点提供者
   * @return 地面位置，如果找不到则返回null
   */
  private PathPosition findGroundBelow(PathPosition position, NavigationPointProvider provider) {
    // 向下搜索最多10格，寻找固体方块
    for (int i = 0; i <= 10; i++) {
      PathPosition checkPos = position.subtract(0, i, 0);
      BukkitNavigationPoint point = (BukkitNavigationPoint) provider.getNavigationPoint(checkPos);
      
      if (point.getMaterial().isSolid()) {
        return checkPos;
      }
    }
    return null; // 找不到地面
  }
}
