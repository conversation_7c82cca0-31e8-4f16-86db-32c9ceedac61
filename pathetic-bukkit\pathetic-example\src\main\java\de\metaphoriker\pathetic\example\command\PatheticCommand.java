package de.metaphoriker.pathetic.example.command;

import de.metaphoriker.pathetic.api.pathing.Pathfinder;
import de.metaphoriker.pathetic.api.pathing.result.PathfinderResult;
import de.metaphoriker.pathetic.api.wrapper.PathPosition;
import de.metaphoriker.pathetic.bukkit.mapper.BukkitMapper;
import de.metaphoriker.pathetic.example.filter.WalkableFilter;
import io.papermc.paper.entity.TeleportFlag;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletionStage;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.command.Command;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabExecutor;
import org.bukkit.entity.Player;
import org.bukkit.entity.ItemDisplay;
import org.bukkit.inventory.ItemStack;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.util.Transformation;
import org.joml.AxisAngle4f;
import org.joml.Vector3f;
import java.util.ArrayList;

public class PatheticCommand implements TabExecutor {

  // Map to store player sessions using their unique IDs
  private static final Map<UUID, PlayerSession> SESSION_MAP = new HashMap<>();

  // Pathfinder instance to handle pathfinding logic
  private final Pathfinder pathfinder;

  // 新的字段
  private final org.bukkit.plugin.Plugin plugin;
  private final Map<UUID, List<PathPosition>> pathCache = new HashMap<>();

  // Constructor to initialize the pathfinder
  public PatheticCommand(Pathfinder pathfinder, org.bukkit.plugin.Plugin plugin) {
    this.pathfinder = pathfinder;
    this.plugin = plugin;
  }

  // Handle command execution
  @Override
  public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {

    // Ensure the sender is a player
    if (!(sender instanceof Player))
      return false;

    // // Ensure the command has exactly one argument
    // if (args.length != 1)
    // return false;

    // Cast sender to Player
    Player player = (Player) sender;

    // Retrieve or create a new player session
    PlayerSession playerSession = SESSION_MAP.computeIfAbsent(player.getUniqueId(), k -> new PlayerSession());

    // Handle different commands
    switch (args[0]) {
      case "pos1":
        // Set position 1 to the player's current location
        playerSession.setPos1(player.getLocation());
        player.sendMessage("Position 1 set to " + player.getLocation());
        break;

      case "pos2":
        // Set position 2 to the player's current location
        playerSession.setPos2(player.getLocation());
        player.sendMessage("Position 2 set to " + player.getLocation());
        break;

      case "start":
        // Ensure both positions are set before starting pathfinding
        if (!playerSession.isComplete()) {
          player.sendMessage("Set both positions first!");
          return false;
        }

        // 检查是否有额外的参数
        String displayMode = args.length > 1 ? args[1] : "blocks";

        // Convert Bukkit locations to pathfinding positions
        PathPosition start = BukkitMapper.toPathPosition(playerSession.getPos1());
        PathPosition target = BukkitMapper.toPathPosition(playerSession.getPos2());

        // Inform the player that pathfinding is starting
        player.sendMessage("Starting pathfinding... [Distance: " + start.distance(target) + "]");

        // 记录开始时间
        long startTime = System.currentTimeMillis();

        CompletionStage<PathfinderResult> pathfindingResult = pathfinder.findPath(start, target,
            List.of(new WalkableFilter()));

        // Handle the pathfinding result
        pathfindingResult.thenAccept(
            result -> {
              // 计算耗时（秒）
              double elapsedTime = (System.currentTimeMillis() - startTime) / 1000.0;
              new BukkitRunnable() {
                @Override
                public void run() {
                  player.sendMessage("State: " + result.getPathState().name());
                  player.sendMessage("Path length: " + result.getPath().length());
                  player.sendMessage("寻路耗时: " + elapsedTime + " 秒");

                  // If pathfinding is successful, show the path to the player
                  if (result.successful() || result.hasFallenBack()) {
                    if ("blocks".equalsIgnoreCase(displayMode)) {
                      // log
                      player.sendMessage("Starting block display...");
                      // 方块展示模式
                      result
                          .getPath()
                          .forEach(
                              position -> {
                                Location location = BukkitMapper.toLocation(position);
                                player.sendBlockChange(
                                    location, Material.YELLOW_STAINED_GLASS.createBlockData());
                              });
                    } else if ("entity".equalsIgnoreCase(displayMode)) {
                      // log
                      player.sendMessage("Starting entity movement...");
                      // 实体移动模式
                      List<PathPosition> path = new ArrayList<>();
                      result.getPath().forEach(path::add);

                      // 缓存路径
                      pathCache.put(player.getUniqueId(), path);

                      // 创建钻石物品展示实体
                      Location startLoc = BukkitMapper.toLocation(path.get(0));
                      ItemDisplay itemDisplay = (ItemDisplay) player.getWorld().spawn(startLoc, ItemDisplay.class);
                      ItemStack diamondItem = new ItemStack(Material.DIAMOND);
                      itemDisplay.setGlowing(true);
                      itemDisplay.setTeleportDuration(5);
                      itemDisplay.setItemStack(diamondItem);
                      // 让玩家骑上itemDisplay
                      itemDisplay.addPassenger(player);
                      player.sendMessage("你现在正骑在展示实体上。");

                      // 设置物品展示的变换
                      Transformation transformation = new Transformation(
                          new Vector3f(0, 0, 0),
                          new AxisAngle4f(0, 0, 0, 0),
                          new Vector3f(0.5f, 0.5f, 0.5f),
                          new AxisAngle4f(0, 0, 0, 0));
                      itemDisplay.setTransformation(transformation);

                      // 开始沿路径移动
                      moveAlongPath(player, itemDisplay, path, 0);
                    }
                  } else {
                    player.sendMessage("Path not found!");
                  }
                }
              }.runTask(plugin);

            });
        break;

      default:
        player.sendMessage("Invalid argument!");
        return false;
    }

    return false;
  }

  // Provide tab completion for the command
  @Override
  public List<String> onTabComplete(
      CommandSender sender, Command command, String label, String[] args) {
    if (args.length == 1) {
      return Arrays.asList("pos1", "pos2", "start");
    } else if (args.length == 2 && "start".equals(args[0])) {
      return Arrays.asList("blocks", "entity");
    }
    return new ArrayList<>();
  }

  // Class to manage player session data
  private static class PlayerSession {

    private Location pos1;
    private Location pos2;

    public void setPos1(Location pos1) {
      this.pos1 = pos1;
    }

    public void setPos2(Location pos2) {
      this.pos2 = pos2;
    }

    // Check if both positions are set
    public boolean isComplete() {
      return pos1 != null && pos2 != null;
    }

    public Location getPos1() {
      return pos1;
    }

    public Location getPos2() {
      return pos2;
    }
  }

  // 新的方法
  private void moveAlongPath(Player player, ItemDisplay itemDisplay, List<PathPosition> path, int currentIndex) {
    if (currentIndex >= path.size()) {
      // 路径结束，清除缓存
      pathCache.remove(player.getUniqueId());
      itemDisplay.remove();
      player.sendMessage("路径展示完成！");
      return;
    }

    // 获取当前位置
    Location targetLoc = BukkitMapper.toLocation(path.get(currentIndex));
    targetLoc.add(0.5, 0.5, 0.5); // 使实体居中于方块

    // 确保在主线程执行实体操作
    new BukkitRunnable() {
      @Override
      public void run() {
        itemDisplay.teleport(targetLoc, TeleportFlag.EntityState.RETAIN_PASSENGERS);
        player.sendMessage("正在移动到" + targetLoc.toString()); // 显示当前移动的位置信息给玩家

        // 安排下一个移动
        new BukkitRunnable() {
          @Override
          public void run() {
            moveAlongPath(player, itemDisplay, path, currentIndex + 1);
          }
        }.runTaskLater(plugin, 5L); // 5 tick (0.25秒) 后移动到下一个位置
      }
    }.runTask(plugin); // 立即在主线程上执行
  }
}
